'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { GdprScanResult, ManualReviewItem, ManualReviewSummary } from '@/types/gdpr';
import { GdprApiService } from '@/services/gdpr-api';
import {
  Clock,
  AlertTriangle,
  CheckCircle,
  FileText,
  Scale,
  User,
  Users,
  Globe,
  Shield,
  Eye,
} from 'lucide-react';

interface ManualReviewDashboardProps {
  recentScans: GdprScanResult[];
  onReviewUpdate?: (scanId: string, updatedScore: number) => void;
}

export function ManualReviewDashboard({ recentScans, onReviewUpdate }: ManualReviewDashboardProps) {
  const [selectedScan, setSelectedScan] = useState<string | null>(null);
  const [reviewNotes, setReviewNotes] = useState<Record<string, string>>({});
  const [reviewAssessments, setReviewAssessments] = useState<Record<string, string>>({});
  const [reviewStatuses, setReviewStatuses] = useState<Record<string, 'pending' | 'completed'>>({});
  const [savingReviews, setSavingReviews] = useState<Record<string, boolean>>({});

  // Extract manual review items from the most recent scan only
  const getAllManualReviewItems = (): ManualReviewItem[] => {
    const items: ManualReviewItem[] = [];

    // Only process the most recent scan to avoid showing duplicate manual review items
    if (Array.isArray(recentScans) && recentScans.length > 0) {
      const mostRecentScan = recentScans[0]; // Scans are ordered by timestamp desc
      const checks = mostRecentScan?.checks || [];

      console.log(`📊 Processing manual review items from most recent scan: ${mostRecentScan.scanId}`);
      console.log(`📊 Total checks in scan: ${checks.length}`);

      const manualReviewChecks = checks.filter((check) => check?.manualReviewRequired);
      console.log(`📊 Manual review checks found: ${manualReviewChecks.length}`);
      console.log(`📊 Manual review rule IDs:`, manualReviewChecks.map(c => c.ruleId));

      manualReviewChecks.forEach((check) => {
        // Determine review status from database data or local state
        const itemKey = `${mostRecentScan.scanId}|${check.ruleId}`;
        let reviewStatus: 'pending' | 'in_progress' | 'completed' = 'pending';

        // Check local state first, then database data
        if (reviewStatuses[itemKey] === 'completed') {
          reviewStatus = 'completed';
        } else if (check.manualReviewCompleted) {
          reviewStatus = 'completed';
          // Sync local state with database state
          setReviewStatuses((prev) => ({ ...prev, [itemKey]: 'completed' }));
        }

        items.push({
          ruleId: check.ruleId,
          ruleName: check.ruleName,
          category: check.category,
          automatedFindings: check.evidence || [],
          reviewStatus,
          reviewNotes: check.manualReviewNotes,
          reviewerAssessment: check.manualReviewAssessment as any,
          reviewDate: check.manualReviewedAt,
          reviewerId: check.manualReviewedBy,
          scanId: mostRecentScan.scanId,
          targetUrl: mostRecentScan.targetUrl,
          scanDate: mostRecentScan.timestamp,
        } as ManualReviewItem & { scanId: string; targetUrl: string; scanDate: string });
      });
    }

    console.log(`📊 Final manual review items count: ${items.length}`);
    return items;
  };

  const manualReviewItems = getAllManualReviewItems();

  // Calculate summary statistics based on database data
  const getManualReviewSummary = (): ManualReviewSummary => {
    const totalItems = manualReviewItems.length;

    // Count based on actual database status
    let completed = 0;
    let pendingReview = 0;
    let inProgress = 0;

    manualReviewItems.forEach((item) => {
      if (item.reviewStatus === 'completed') {
        completed++;
      } else if (item.reviewStatus === 'in_progress') {
        inProgress++;
      } else {
        pendingReview++;
      }
    });

    const complianceRate = totalItems > 0 ? (completed / totalItems) * 100 : 0;

    console.log(`📊 Manual Review Summary: ${completed}/${totalItems} completed (${Math.round(complianceRate)}%)`);

    return {
      totalItems,
      pendingReview,
      inProgress,
      completed,
      complianceRate,
    };
  };

  const summary = getManualReviewSummary();

  const handleReviewUpdate = (itemKey: string, field: 'notes' | 'assessment', value: string) => {
    if (field === 'notes') {
      setReviewNotes((prev) => ({ ...prev, [itemKey]: value }));
    } else {
      setReviewAssessments((prev) => ({ ...prev, [itemKey]: value }));
    }
  };

  const handleSaveReview = async (itemKey: string) => {
    const assessment = reviewAssessments[itemKey];
    const notes = reviewNotes[itemKey];

    if (!assessment) {
      console.warn('Cannot save review without assessment');
      return;
    }

    setSavingReviews((prev) => ({ ...prev, [itemKey]: true }));

    try {
      // Parse itemKey to get scanId and ruleId (using | separator to avoid UUID conflicts)
      const [scanId, ruleId] = itemKey.split('|');

      // Validate that we have both scanId and ruleId
      if (!scanId || !ruleId) {
        throw new Error(`Invalid itemKey format: ${itemKey}. Expected format: scanId|ruleId`);
      }

      // Validate UUID format for scanId
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(scanId)) {
        throw new Error(`Invalid scan ID format: ${scanId}. Expected UUID format.`);
      }

      console.log('💾 Saving manual review:', {
        scanId,
        ruleId,
        assessment,
        notes,
        timestamp: new Date().toISOString(),
      });

      // Call real API to save review
      const result = await GdprApiService.updateManualReview(
        scanId,
        ruleId,
        assessment,
        notes,
        'Legal Reviewer'
      );

      if (result.success) {
        // Update review status to completed
        setReviewStatuses((prev) => ({ ...prev, [itemKey]: 'completed' }));

        console.log('✅ Manual review saved successfully');
        console.log('📊 Updated overall score:', result.updatedScore);

        // Trigger a refresh of the parent component to reload data from database
        if (onReviewUpdate) {
          onReviewUpdate(scanId, result.updatedScore || 0);
        }

        // Show success message
        console.log('💾 Manual review data persisted to database');
      } else {
        throw new Error(result.message || 'Failed to save review');
      }
    } catch (error) {
      console.error('❌ Failed to save manual review:', error);
      // You might want to show a toast notification here
    } finally {
      setSavingReviews((prev) => ({ ...prev, [itemKey]: false }));
    }
  };

  const getManualReviewIcon = (ruleId: string) => {
    switch (ruleId) {
      case 'GDPR-013':
        return <Users className="h-4 w-4" />; // Special Category Data
      case 'GDPR-014':
        return <Shield className="h-4 w-4" />; // Children's Consent
      case 'GDPR-016':
        return <Globe className="h-4 w-4" />; // International Transfers
      case 'GDPR-018':
        return <Scale className="h-4 w-4" />; // DPIA
      case 'GDPR-003':
        return <FileText className="h-4 w-4" />; // Privacy Notice Content
      default:
        return <Eye className="h-4 w-4" />;
    }
  };

  const getManualReviewDescription = (ruleId: string) => {
    switch (ruleId) {
      case 'GDPR-013':
        return 'Review automated detection of special category data processing and verify consent mechanisms meet legal requirements.';
      case 'GDPR-014':
        return "Assess age verification mechanisms and parental consent procedures for compliance with children's data protection.";
      case 'GDPR-016':
        return 'Evaluate international data transfer safeguards and adequacy decisions for legal compliance.';
      case 'GDPR-018':
        return 'Review Data Protection Impact Assessment documentation and high-risk processing activities.';
      case 'GDPR-003':
        return 'Assess privacy notice content for legal adequacy and completeness of required information.';
      default:
        return 'Manual legal review required to determine compliance with GDPR requirements.';
    }
  };

  if (manualReviewItems.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Manual Reviews Required</h3>
          <p className="text-muted-foreground">
            All recent GDPR scans have been automatically assessed. No items require manual legal
            review.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalItems}</div>
            <p className="text-xs text-muted-foreground">Requiring manual review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{summary.pendingReview}</div>
            <p className="text-xs text-muted-foreground">Awaiting legal review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{summary.inProgress}</div>
            <p className="text-xs text-muted-foreground">Under review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{summary.completed}</div>
            <p className="text-xs text-muted-foreground">Review complete</p>
          </CardContent>
        </Card>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Review Progress</CardTitle>
          <CardDescription>Overall progress of manual legal reviews</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Completion Rate</span>
              <span>{Math.round(summary.complianceRate)}%</span>
            </div>
            <Progress value={summary.complianceRate} className="w-full" />
            <p className="text-xs text-muted-foreground">
              {summary.completed} of {summary.totalItems} items reviewed
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Manual Review Items */}
      <Card>
        <CardHeader>
          <CardTitle>Manual Review Items</CardTitle>
          <CardDescription>Items requiring human legal expertise assessment</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {manualReviewItems.map((item, index) => {
              const itemKey = `${(item as any).scanId}|${item.ruleId}`;
              return (
                <div key={itemKey} className="border rounded-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {getManualReviewIcon(item.ruleId)}
                      <div>
                        <h4 className="font-semibold">{item.ruleName}</h4>
                        <p className="text-sm text-muted-foreground">
                          {item.ruleId} • {(item as any).targetUrl}
                        </p>
                      </div>
                    </div>
                    <Badge
                      variant={reviewStatuses[itemKey] === 'completed' ? 'default' : 'outline'}
                      className={reviewStatuses[itemKey] === 'completed' ? 'bg-green-500 text-white' : ''}
                    >
                      {reviewStatuses[itemKey] === 'completed' ? (
                        <>
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Completed
                        </>
                      ) : (
                        <>
                          <Clock className="h-3 w-3 mr-1" />
                          Pending
                        </>
                      )}
                    </Badge>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h5 className="text-sm font-medium mb-2">Legal Review Required:</h5>
                      <p className="text-sm text-muted-foreground">
                        {getManualReviewDescription(item.ruleId)}
                      </p>
                    </div>

                    <div>
                      <h5 className="text-sm font-medium mb-2">Automated Findings:</h5>
                      <div className="space-y-2">
                        {item.automatedFindings.map((finding, findingIndex) => (
                          <div key={findingIndex} className="bg-muted p-3 rounded text-sm">
                            <strong>{finding.type}:</strong> {finding.description}
                            {finding.location && (
                              <div className="text-xs text-muted-foreground mt-1">
                                Location: {finding.location}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`assessment-${itemKey}`}>Legal Assessment</Label>
                        <Select
                          value={reviewAssessments[itemKey] || ''}
                          onValueChange={(value) =>
                            handleReviewUpdate(itemKey, 'assessment', value)
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select assessment" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="compliant">Compliant</SelectItem>
                            <SelectItem value="non-compliant">Non-Compliant</SelectItem>
                            <SelectItem value="partially-compliant">Partially Compliant</SelectItem>
                            <SelectItem value="needs-review">Needs Review</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`notes-${itemKey}`}>Review Notes</Label>
                        <Textarea
                          id={`notes-${itemKey}`}
                          placeholder="Enter legal review notes..."
                          value={reviewNotes[itemKey] || ''}
                          onChange={(e) => handleReviewUpdate(itemKey, 'notes', e.target.value)}
                          rows={3}
                        />
                      </div>
                    </div>

                    <div className="flex justify-end space-x-2">
                      {reviewStatuses[itemKey] === 'completed' && (
                        <Badge variant="default" className="bg-green-500">
                          ✓ Completed
                        </Badge>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={!reviewAssessments[itemKey] || savingReviews[itemKey]}
                        onClick={() => handleSaveReview(itemKey)}
                      >
                        {savingReviews[itemKey] ? 'Saving...' : 'Save Review'}
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Legal Guidance */}
      <Alert>
        <Scale className="h-4 w-4" />
        <AlertDescription>
          <strong>Legal Expertise Required:</strong> These items require assessment by qualified
          legal professionals familiar with GDPR requirements. Automated analysis provides evidence,
          but human judgment is needed for final compliance determination.
        </AlertDescription>
      </Alert>
    </div>
  );
}
